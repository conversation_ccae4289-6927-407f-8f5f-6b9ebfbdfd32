"""
用户认证API路由
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.core.auth import get_current_user, get_current_active_user
from app.core.logging import get_logger
from app.services.auth_service import auth_service
from app.models.user import (
    UserCreate, 
    UserLogin, 
    UserResponse, 
    UserProfile, 
    Token
)

logger = get_logger(__name__)
router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(
    user_create: UserCreate,
    db: Session = Depends(get_db)
):
    """用户注册"""
    try:
        user = auth_service.create_user(db, user_create)
        return UserResponse.model_validate(user)
    except Exception as e:
        logger.error(f"用户注册失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/login", response_model=Token)
async def login(
    user_login: UserLogin,
    db: Session = Depends(get_db)
):
    """用户登录"""
    try:
        # 认证用户
        user = auth_service.authenticate_user(db, user_login)
        
        # 创建访问令牌
        access_token, expire_time = auth_service.create_access_token(
            user.id, user.email
        )
        
        # 计算过期时间（秒）
        from datetime import datetime
        expires_in = int((expire_time - datetime.utcnow()).total_seconds())
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=expires_in,
            user=UserProfile.model_validate(user)
        )
        
    except Exception as e:
        logger.error(f"用户登录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="邮箱或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(
    current_user = Depends(get_current_user)
):
    """获取当前用户信息"""
    return UserProfile.model_validate(current_user)


@router.post("/refresh-token", response_model=Token)
async def refresh_token(
    current_user = Depends(get_current_active_user)
):
    """刷新访问令牌"""
    try:
        # 创建新的访问令牌
        access_token, expire_time = auth_service.create_access_token(
            current_user.id, current_user.email
        )
        
        # 计算过期时间（秒）
        from datetime import datetime
        expires_in = int((expire_time - datetime.utcnow()).total_seconds())
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=expires_in,
            user=UserProfile.model_validate(current_user)
        )
        
    except Exception as e:
        logger.error(f"刷新令牌失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌失败"
        )


@router.post("/logout")
async def logout():
    """用户登出"""
    # 在无状态JWT系统中，登出主要由客户端处理（删除本地令牌）
    # 这里返回成功响应
    return {"message": "登出成功"}


@router.get("/points")
async def get_user_points(
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户积分"""
    # 获取最新的用户积分数据
    fresh_user = auth_service.get_user_by_id(db, current_user.id)
    if not fresh_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 返回前端期望的数据结构，使用最新的积分数据
    return {
        "stats": {
            "currentBalance": fresh_user.points,
            "totalEarned": fresh_user.points,  # 简化实现，实际应该从积分记录计算
            "totalConsumed": 0,  # 简化实现，实际应该从积分记录计算
            "totalRecords": 1  # 简化实现，实际应该从积分记录计算
        },
        "records": [
            {
                "id": 1,
                "type": "当前积分",
                "amount": fresh_user.points,
                "balance": fresh_user.points,
                "created_at": fresh_user.created_at.isoformat() if hasattr(fresh_user, 'created_at') else None,
                "description": f"当前账户积分余额"
            }
        ]
    }


@router.post("/checkin")
async def daily_checkin(
    current_user = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """每日签到"""
    try:
        # 简单的签到逻辑：每日签到获得10积分
        # 实际应用中应该检查是否已经签到过
        checkin_points = 10
        
        auth_service.update_user_points(db, current_user.id, checkin_points)
        
        # 重新获取用户信息
        updated_user = auth_service.get_user_by_id(db, current_user.id)
        
        return {
            "message": "签到成功",
            "points_earned": checkin_points,
            "total_points": updated_user.points
        }
        
    except Exception as e:
        logger.error(f"签到失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="签到失败"
        )


@router.get("/stats")
async def get_auth_stats(
    db: Session = Depends(get_db)
):
    """获取认证统计信息（公开接口）"""
    try:
        from app.models.user import User
        
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        verified_users = db.query(User).filter(User.is_verified == True).count()
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "verified_users": verified_users,
            "max_users": auth_service.settings.max_users
        }
        
    except Exception as e:
        logger.error(f"获取认证统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )
